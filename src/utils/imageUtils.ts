import Taro from '@tarojs/taro'

/**
 * 图片处理工具类
 */
export class ImageUtils {
  /**
   * 上传文件到服务器
   * @param tempFilePath 临时文件路径
   * @returns Promise<string> 文件ID
   */
  static async uploadFile(tempFilePath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        Taro.uploadFile({
          url: '/api/v1/file/upload', // 使用统一的文件上传接口
          filePath: tempFilePath,
          name: 'file',
          header: {
            'Accept': 'application/json',
          },
          success: (res) => {
            try {
              const data = JSON.parse(res.data)
              if (data.state === 200 && data.results) {
                resolve(data.results)
              } else {
                reject(new Error(data.msg || '上传失败'))
              }
            } catch (error) {
              console.error('解析上传响应失败:', error)
              reject(new Error('上传响应格式错误'))
            }
          },
          fail: (error) => {
            console.error('文件上传失败:', error)
            let errorMessage = '文件上传失败'
            if (error.errMsg) {
              if (error.errMsg.includes('timeout')) {
                errorMessage = '上传超时，请检查网络连接'
              } else if (error.errMsg.includes('fail')) {
                errorMessage = '网络连接失败，请检查网络设置'
              }
            }
            reject(new Error(errorMessage))
          }
        })
      } catch (error) {
        console.error('上传文件失败:', error)
        reject(new Error('上传文件失败'))
      }
    })
  }

  /**
   * 格式化文件资源URL
   * @param fileId 文件ID
   * @returns 完整的文件URL
   */
  static formatResource(fileId: string): string {
    return `/api/v1/file/download?objectName=${fileId}`
  }

  /**
   * 根据文件扩展名获取 MIME 类型
   * @param extension 文件扩展名
   * @returns MIME 类型字符串
   */
  private static getMimeType(extension: string): string {
    const mimeTypes: Record<string, string> = {
      'png': 'image/png',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'gif': 'image/gif',
      'webp': 'image/webp',
      'bmp': 'image/bmp',
      'svg': 'image/svg+xml'
    }
    
    return mimeTypes[extension] || 'image/png'
  }

  /**
   * 获取文件信息
   * @param filePath 文件路径
   * @returns Promise<Taro.getFileInfo.SuccessCallbackResult>
   */
  static async getFileInfo(filePath: string): Promise<Taro.getFileInfo.SuccessCallbackResult> {
    return new Promise((resolve, reject) => {
      Taro.getFileInfo({
        filePath,
        success: resolve,
        fail: reject
      })
    })
  }

  /**
   * 检查文件大小是否超过限制
   * @param filePath 文件路径
   * @param maxSize 最大文件大小（字节），默认 10MB
   * @returns Promise<boolean> 是否超过限制
   */
  static async isFileSizeExceeded(filePath: string, maxSize: number = 10 * 1024 * 1024): Promise<boolean> {
    try {
      const fileInfo = await ImageUtils.getFileInfo(filePath)
      return fileInfo.size > maxSize
    } catch (error) {
      console.error('获取文件信息失败:', error)
      return false
    }
  }

  /**
   * 格式化文件大小
   * @param bytes 字节数
   * @returns 格式化后的文件大小字符串
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
  }
}

/**
 * 便捷函数：上传文件
 */
export const uploadFile = ImageUtils.uploadFile

/**
 * 便捷函数：格式化文件资源URL
 */
export const formatResource = ImageUtils.formatResource

/**
 * 便捷函数：检查文件大小
 */
export const checkFileSize = ImageUtils.isFileSizeExceeded
