<script setup lang="ts">
import { checkFileSize, ImageUtils, tempFileToBase64Pure } from '@/utils/imageUtils'
import http from '@/utils/request'
import Taro, { useDidShow } from '@tarojs/taro'
import { ref } from 'vue'

const avatarUrl = ref('https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0')
const nickname = ref('')
const isUploadingAvatar = ref(false)

async function onChooseAvatar(e: any) {
  const { avatarUrl: tempAvatarUrl } = e.detail

  // 防止重复上传
  if (isUploadingAvatar.value) {
    return
  }

  try {
    isUploadingAvatar.value = true

    // 显示加载提示
    Taro.showLoading({
      title: '处理头像中...',
      mask: true,
    })

    // 检查文件大小（限制为 2MB）
    const isFileTooLarge = await checkFileSize(tempAvatarUrl, 2 * 1024 * 1024)
    if (isFileTooLarge) {
      const fileInfo = await ImageUtils.getFileInfo(tempAvatarUrl)
      const fileSize = ImageUtils.formatFileSize(fileInfo.size)

      Taro.hideLoading()
      Taro.showModal({
        title: '文件过大',
        content: `图片大小为 ${fileSize}，请选择小于 2MB 的图片`,
        showCancel: false,
      })
      return
    }

    // 将临时文件转换为 base64
    const base64Data = await tempFileToBase64Pure(tempAvatarUrl)

    // 更新显示的头像
    avatarUrl.value = tempAvatarUrl

    // 隐藏加载提示，显示上传提示
    Taro.hideLoading()
    Taro.showLoading({
      title: '上传头像中...',
      mask: true,
    })

    // 发送 base64 数据到服务器
    await http.post('/api/operator/renew-staff-info', {
      headImgUrl: base64Data,
    })

    Taro.hideLoading()
    Taro.showToast({
      title: '头像已更新',
      icon: 'success',
    })
  }
  catch (error) {
    console.error('更新头像信息失败:', error)

    Taro.hideLoading()

    // 根据错误类型显示不同的提示
    let errorMessage = '头像更新失败'
    if (error instanceof Error) {
      if (error.message.includes('读取文件失败')) {
        errorMessage = '图片读取失败，请重新选择'
      }
      else if (error.message.includes('转换')) {
        errorMessage = '图片处理失败，请重新选择'
      }
      else if (error.message.includes('网络')) {
        errorMessage = '网络异常，请检查网络连接'
      }
    }

    Taro.showToast({
      title: errorMessage,
      icon: 'error',
      duration: 2000,
    })
  }
  finally {
    isUploadingAvatar.value = false
  }
}

async function autoGetUserNickname() {
  try {
    const { authSetting } = await Taro.getSetting()

    if (authSetting['scope.userInfo']) {
      const { userInfo } = await Taro.getUserInfo({
        withCredentials: true,
      })

      if (userInfo && userInfo.nickName) {
        nickname.value = userInfo.nickName
      }
    }
  }
  catch (error) {
    console.error('自动获取昵称失败:', error)
  }
}

async function getUserNickname() {
  try {
    const { authSetting } = await Taro.getSetting()

    if (!authSetting['scope.userInfo']) {
      const { confirm } = await Taro.showModal({
        title: '授权提示',
        content: '需要获取您的昵称信息，是否同意授权？',
        confirmText: '同意',
        cancelText: '取消',
      })

      if (!confirm) {
        Taro.showToast({
          title: '需要授权才能获取昵称',
          icon: 'none',
        })
        return
      }
    }

    const { userInfo } = await Taro.getUserInfo({
      withCredentials: true,
    })

    if (userInfo && userInfo.nickName) {
      nickname.value = userInfo.nickName

      Taro.showToast({
        title: '昵称获取成功',
        icon: 'success',
      })
    }
  }
  catch (error) {
    console.error('获取昵称失败:', error)
    Taro.showToast({
      title: '获取昵称失败',
      icon: 'none',
    })
  }
}

useDidShow(() => {
  autoGetUserNickname()
})
</script>

<template>
  <div class="profile-container">
    <div class="profile-content">
      <div class="avatar-section">
        <button class="avatar-btn" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
          <image class="avatar" :src="avatarUrl" mode="aspectFill" />
          <text class="avatar-tip">
            点击更换头像
          </text>
        </button>
      </div>

      <div class="nickname-section">
        <text class="label">
          昵称
        </text>
        <div class="nickname-display" :class="{ clickable: !nickname }" @click="!nickname ? getUserNickname() : null">
          <text v-if="nickname" class="nickname-text">
            {{ nickname }}
          </text>
          <text v-else class="nickname-placeholder">
            点击获取昵称
          </text>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.profile-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

.profile-content {
  background-color: #fff;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16px;

  .avatar-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: none;
    border: none !important;
    outline: none !important;
    cursor: pointer;

    &::after {
      border: none !important;
      border-radius: 0 !important;
    }

    .avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      border: 2px solid #e5e5e5;
      box-shadow: 0 4px 12px rgba(0, 172, 151, 0.1);
    }

    .avatar-tip {
      font-size: 12px;
      color: #666;
      margin-top: 8px;
      font-weight: 500;
    }
  }
}

.nickname-section {
  display: flex;
  align-items: center;
  gap: 16px;

  .label {
    font-size: 16px;
    color: #374151;
    font-weight: 600;
    flex-shrink: 0;
    min-width: 40px;
  }

  .nickname-display {
    flex: 1;
    height: 44px;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    padding: 0 12px;
    font-size: 16px;
    background-color: #fafafa;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;

    &.clickable {
      cursor: pointer;

      &:hover {
        border-color: #00ac97;
        background-color: rgba(0, 172, 151, 0.05);
      }
    }

    .nickname-text {
      color: #333;
      font-weight: 500;
      flex: 1;
    }

    .nickname-placeholder {
      color: #999;
      font-size: 14px;
      flex: 1;
    }

  }
}
</style>
