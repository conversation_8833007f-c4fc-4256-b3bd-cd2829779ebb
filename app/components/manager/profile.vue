<script setup lang="ts">
import { positionMap } from '@/utils/constants'
import { useImageUploader } from '@/composables/useCustomUploader'

const mode = defineModel<'view' | 'edit'>('mode')
const profile = defineModel<HostProfileForm>('profile')

const positionOptions = Object.entries(positionMap).map(([value, text]) => ({ text, value }))

const showPicker = ref(false)
const showAvatarActionSheet = ref(false)

const route = useRoute()

// 使用统一的图片上传逻辑
const {
    pictures: avatarPictures,
    uploadedFileIds,
    isImageLoading,
    handleBeforeRead,
    handlePreview,
    handleDelete,
    clearPictures,
    getUploadedFileIds,
} = useImageUploader({
    maxCount: 1,
    maxFileSize: 10, // 10MB
    onUploadSuccess: (fileId) => {
        console.log('头像上传成功:', fileId)
    },
    onUploadComplete: (fileIds) => {
        console.log('头像上传完成:', fileIds)
    },
})

// 头像选择方式的操作选项
const avatarActions = [
    { name: '拍照', value: 'camera' },
    { name: '从相册选择', value: 'album' },
    { name: '使用微信头像', value: 'wechat' },
]

// 处理头像选择方式
function handleAvatarActionSelect(action: { name: string; value: string }) {
    showAvatarActionSheet.value = false

    switch (action.value) {
        case 'camera':
            // 触发相机拍照
            triggerFileInput('camera')
            break
        case 'album':
            // 触发相册选择
            triggerFileInput('album')
            break
        case 'wechat':
            // 使用微信头像授权
            handleWechatAvatar()
            break
    }
}

// 触发文件选择
function triggerFileInput(source: 'camera' | 'album') {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'

    if (source === 'camera') {
        input.capture = 'environment' // 使用后置摄像头
    }

    input.onchange = async (e) => {
        const file = (e.target as HTMLInputElement).files?.[0]
        if (file) {
            await handleBeforeRead(file)
        }
    }

    input.click()
}

// 处理微信头像授权
async function handleWechatAvatar() {
    try {
        // 这里需要根据实际的微信授权逻辑来实现
        // 暂时显示提示，具体实现需要微信 JS-SDK
        showToast('微信头像授权功能开发中')

        // TODO: 实现微信头像授权逻辑
        // const wx = await useWxBridge({
        //     jsApiList: ['chooseImage']
        // })
        //
        // wx.chooseImage({
        //     count: 1,
        //     sizeType: ['compressed'],
        //     sourceType: ['album', 'camera'],
        //     success: async (res) => {
        //         const tempFilePath = res.localIds[0]
        //         // 将临时文件转换为可上传的文件对象
        //         // 然后调用 handleBeforeRead
        //     }
        // })
    } catch (error) {
        console.error('微信头像授权失败:', error)
        showFailToast('微信头像授权失败')
    }
}

// 暴露给父组件的接口
defineExpose({
    getUploadedFileIds,
    clearPictures,
    avatarPictures,
})

const { userInfo } = useUserStore()

const ageAndGender = computed(() => {
    return parseIDCard(userInfo!.idCard)
})
</script>

<template>
    <div class="flex flex-col items-center space-y-8px pt-8px pb-24px">
        <!-- 编辑模式：可点击选择头像 -->
        <div v-if="mode === 'edit'" class="relative">
            <div
                class="cursor-pointer relative"
                @click="showAvatarActionSheet = true"
            >
                <van-image
                    :src="avatarPictures.length > 0 ? avatarPictures[0]?.url : formatHeadImage(profile!.headImage, ageAndGender?.gender)"
                    fit="cover"
                    w-56px
                    h-56px
                    class="of-hidden rounded-4px"
                    border="2px solid #fff"
                >
                    <template #loading>
                        <shared-unified-loading size="small" :rainbow="false" />
                    </template>
                </van-image>

                <!-- 上传中的遮罩 -->
                <div v-if="isImageLoading" class="absolute inset-0 bg-black bg-opacity-50 rounded-4px flex items-center justify-center">
                    <shared-unified-loading size="small" :rainbow="false" text="上传中..." />
                </div>

                <!-- 编辑图标 -->
                <div class="absolute -bottom-2px -right-2px w-20px h-20px bg-primary-6 rounded-full flex items-center justify-center">
                    <div class="i-custom-edit w-12px h-12px text-white"></div>
                </div>
            </div>
        </div>

        <!-- 查看模式：只显示头像 -->
        <van-image
            v-else
            :src="formatHeadImage(profile!.headImage, ageAndGender?.gender)"
            fit="cover"
            w-56px
            h-56px
            class="of-hidden rounded-4px"
            border="2px solid #fff"
        >
            <template #loading>
                <shared-unified-loading size="small" :rainbow="false" />
            </template>
        </van-image>

        <span text-t-4 text-13px>头像</span>
    </div>

    <!-- 头像选择方式的动作面板 -->
    <van-action-sheet
        v-model:show="showAvatarActionSheet"
        :actions="avatarActions"
        cancel-text="取消"
        @select="handleAvatarActionSelect"
    />

    <div class="flex-1 divide-y">
        <div class="flex space-x-16px text-16px text-t-5 py-16px">
            <p class="w-72px shrink-0">姓名</p>
            <p v-if="mode === 'view'">{{ profile!.name }}</p>
            <van-field v-else v-model="profile!.name" placeholder="请输入姓名" class="p-0!" clearable />
        </div>

        <div v-if="route.path.includes('manager')" class="flex space-x-16px text-16px text-t-5 py-16px">
            <p class="w-72px shrink-0">职务</p>
            <p v-if="mode === 'view'">{{ positionMap[profile?.duties as keyof typeof positionMap] }}</p>
            <p v-else class="flex-1 flex items-center pr-8px" @click="showPicker = true">
                <span v-if="!profile?.duties" class="flex-1 text-t-2 text-14px">请选择您的职务</span>
                <span v-else class="flex-1">{{ positionMap[profile?.duties as keyof typeof positionMap] }}</span>
                <span class="i-radix-icons:chevron-down text-t-3"></span>
            </p>
            <teleport to="body">
                <van-popup v-model:show="showPicker" position="bottom">
                    <van-picker
                        :columns="positionOptions"
                        @confirm="profile!.duties = $event.selectedOptions[0]?.value; showPicker = false"
                        @cancel="showPicker = false"
                    />
                </van-popup>
            </teleport>
        </div>

        <div class="flex space-x-16px text-16px text-t-5 py-16px">
            <p class="w-72px shrink-0">身份证号</p>
            <p v-if="mode === 'view'">{{ encodeIdCard(profile?.idCard || '-') }}</p>
            <van-field v-else v-model="profile!.idCard" placeholder="请输入身份证号" class="p-0!" clearable />
        </div>

        <div class="flex space-x-16px text-16px text-t-5 py-16px">
            <p class="w-72px shrink-0">联系方式</p>
            <p>{{ encodePhone(profile?.phone || '-') }}</p>
        </div>
    </div>
</template>

<style scoped>
:deep(.van-uploader__preview-image) {
    width: 56px;
    height: 56px;
    border-radius: 4px;
}

:deep(.van-image__img) {
    border-radius: 4px;
}
</style>
