<script setup lang="ts">
const mode = ref<'view' | 'edit'>('view')

const { userInfo } = storeToRefs(useUserStore())

const { reset } = useUserStore()
const profile = ref<HostProfileForm>({ ...userInfo.value! })

function handleEdit() {
    mode.value = 'edit'
    profile.value = { ...userInfo.value! }
}

function handleCancel() {
    mode.value = 'view'
    profile.value = { ...userInfo.value! }
}

const profileRef = useTemplateRef('profileRef')

async function handleSave() {
    const { closeLoading } = useLoading({
        message: '保存中',
    })

    try {
        if (!useProfileFormValidate(profile.value))
            return

        let _headImgUrl = ''

        // 检查是否有新上传的头像
        const uploadedFileIds = profileRef.value?.getUploadedFileIds?.() || []
        if (uploadedFileIds.length > 0 && uploadedFileIds[0]) {
            // 使用最新上传的文件ID，转换为完整URL
            _headImgUrl = formatResource(uploadedFileIds[0])
        }

        // 更新 profile 中的头像信息
        if (_headImgUrl) {
            profile.value.headImage = _headImgUrl
        }

        // 准备保存参数
        const params: Record<string, string> = {
            name: profile.value.name,
        }

        if (_headImgUrl) {
            params.headImgUrl = _headImgUrl
        }

        // 调用统一的保存接口 - 根据需求使用 /api/operator/renew-staff-info
        if (_headImgUrl) {
            // 如果有头像更新，调用头像更新接口
            await useWrapFetch<BaseResponse<boolean>>('/api/operator/renew-staff-info', {
                method: 'post',
                body: {
                    headImgUrl: _headImgUrl,
                },
            })
        }

        // 更新其他用户信息
        await useWrapFetch<BaseResponse<boolean>>('/user/update-info', {
            params: {
                name: profile.value.name,
            },
            method: 'get',
        })

        // 更新身份证信息
        if (profile.value.idCard) {
            await useWrapFetch<BaseResponse<string>>('/user/rewIdCard', {
                method: 'PUT',
                params: {
                    idCard: profile.value.idCard,
                },
            })
        }

        // 更新本地用户信息
        userInfo.value = {
            ...profile.value,
        }

        mode.value = 'view'
        showSuccessToast('保存成功')
    } catch (error) {
        console.error('保存失败:', error)
        showFailToast('保存失败，请重试')
    } finally {
        closeLoading()
    }
}

async function handleLogout() {
    reset()

    const wx = await useWxBridge({})

    const dataToBase64 = encodeMessage({
        type: 'user:logout',
        data: 'logout',
    })

    wx?.miniProgram.redirectTo({
        url: `/pages/index/index?message=${dataToBase64}`,
    })
}
</script>

<template>
    <div h-full overflow-auto p-16px pb-0>
        <div class="h-full bg-white p-16px flex flex-col">
            <manager-profile ref="profileRef" v-model:mode="mode" v-model:profile="profile" />

            <div class="pb-40px flex justify-center space-x-24px">
                <template v-if="mode === 'view'">
                    <div flex gap-16px w-full items-center justify-center>
                        <van-button round type="primary" class="!w-165px !h-50px" block @click="handleEdit">修改信息</van-button>
                        <!-- <van-button round class="!w-165px" block @click="handleLogout">退出登录</van-button> -->
                    </div>
                </template>
                <template v-else>
                    <div flex gap-16px>
                        <van-button round type="primary" class="w-132px" @click="handleSave">保存</van-button>
                        <van-button round class="w-132px bg-primary-1! text-primary-6! border-none!" @click="handleCancel">取消</van-button>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>
